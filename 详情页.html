<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>謎·魔术酒吧详情页</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Times New Roman', 'SimSun', serif;
            background-color: #0d0d0d;
            color: #e8e8e8;
            width: 540px;
            margin: 0 auto;
            line-height: 1.5;
            letter-spacing: 0.5px;
        }

        .container {
            width: 100%;
            background-color: #0d0d0d;
        }

        /* 现代哥特风格的通用样式 */
        .section {
            margin-bottom: 40px;
            padding: 0;
        }

        .section-title {
            font-size: 20px;
            font-weight: 300;
            color: #c9a96e;
            margin-bottom: 24px;
            text-align: center;
            letter-spacing: 2px;
            text-transform: uppercase;
            position: relative;
        }

        .section-title::before,
        .section-title::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 40px;
            height: 1px;
            background-color: #c9a96e;
        }

        .section-title::before {
            left: -60px;
        }

        .section-title::after {
            right: -60px;
        }

        /* 金色强调 - 更克制的金色 */
        .gold-accent {
            color: #c9a96e;
        }

        /* 分割线 */
        .divider {
            width: 100%;
            height: 1px;
            background: linear-gradient(90deg, transparent, #333333, transparent);
            margin: 30px 0;
        }

        /* 卡片样式 - 极简设计 */
        .card {
            background-color: #111111;
            border: 1px solid #222222;
            padding: 24px;
            margin-bottom: 20px;
        }

        /* 图片占位符 */
        .image-placeholder {
            background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
            border: 1px solid #333333;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666666;
            font-size: 12px;
            letter-spacing: 1px;
        }

        /* 文字样式 */
        .body-text {
            font-size: 14px;
            line-height: 1.6;
            color: #cccccc;
            margin-bottom: 16px;
        }

        .small-text {
            font-size: 12px;
            color: #999999;
            line-height: 1.4;
        }

        .emphasis-text {
            font-weight: 500;
            color: #ffffff;
        }

        /* 星级评分 - 简约设计 */
        .stars {
            color: #c9a96e;
            font-size: 14px;
            letter-spacing: 2px;
        }

        /* 店铺介绍区域 */
        .shop-intro {
            height: 270px; /* 2:1 比例 */
            background: linear-gradient(180deg, #0d0d0d 0%, #1a1a1a 50%, #0d0d0d 100%);
            border: 1px solid #222222;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 40px;
        }

        .shop-intro-content {
            text-align: center;
            max-width: 400px;
            padding: 0 30px;
        }

        .shop-brand {
            font-size: 32px;
            font-weight: 300;
            color: #c9a96e;
            margin-bottom: 24px;
            letter-spacing: 3px;
            font-family: 'Times New Roman', serif;
        }

        .shop-tagline {
            font-size: 14px;
            color: #cccccc;
            line-height: 1.8;
            margin-bottom: 20px;
            font-weight: 300;
        }

        .shop-description {
            font-size: 13px;
            color: #aaaaaa;
            line-height: 1.6;
            margin-bottom: 24px;
        }

        .shop-location {
            border-top: 1px solid #333333;
            padding-top: 16px;
        }

        .shop-location-label {
            font-size: 10px;
            color: #c9a96e;
            letter-spacing: 2px;
            text-transform: uppercase;
            margin-bottom: 8px;
        }

        .shop-address {
            font-size: 14px;
            color: #ffffff;
            font-weight: 300;
        }

        /* 创始人介绍区域 */
        .founder-section {
            padding: 40px 30px;
            border-top: 1px solid #222222;
        }

        .founder-container {
            display: flex;
            gap: 24px;
            align-items: flex-start;
        }

        .founder-photo {
            width: 120px;
            height: 240px; /* 1:2 比例 */
            background: linear-gradient(180deg, #1a1a1a, #2a2a2a);
            border: 1px solid #333333;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666666;
            font-size: 10px;
            letter-spacing: 1px;
        }

        .founder-info {
            flex: 1;
            padding-top: 8px;
        }

        .founder-name {
            font-size: 24px;
            color: #c9a96e;
            font-weight: 300;
            margin-bottom: 8px;
            letter-spacing: 1px;
        }

        .founder-title {
            font-size: 13px;
            color: #cccccc;
            margin-bottom: 20px;
            font-weight: 300;
        }

        .founder-achievements {
            list-style: none;
        }

        .founder-achievements li {
            font-size: 12px;
            color: #aaaaaa;
            line-height: 1.6;
            margin-bottom: 8px;
            padding-left: 12px;
            position: relative;
        }

        .founder-achievements li::before {
            content: '·';
            position: absolute;
            left: 0;
            color: #c9a96e;
            font-weight: bold;
        }

        /* 剧目介绍区域 */
        .shows-section {
            padding: 40px 30px;
            border-top: 1px solid #222222;
        }

        .show-item {
            margin-bottom: 40px;
            padding-bottom: 30px;
            border-bottom: 1px solid #1a1a1a;
        }

        .show-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .show-container {
            display: flex;
            gap: 20px;
            align-items: flex-start;
        }

        .show-poster {
            width: 100px;
            height: 200px; /* 1:2 比例 */
            background: linear-gradient(180deg, #1a1a1a, #2a2a2a);
            border: 1px solid #333333;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666666;
            font-size: 10px;
            letter-spacing: 1px;
            text-align: center;
        }

        .show-info {
            flex: 1;
        }

        .show-title {
            font-size: 18px;
            color: #c9a96e;
            font-weight: 300;
            margin-bottom: 12px;
            letter-spacing: 1px;
        }

        .show-meta {
            margin-bottom: 16px;
        }

        .show-meta-item {
            font-size: 12px;
            color: #aaaaaa;
            margin-bottom: 4px;
        }

        .show-meta-label {
            color: #c9a96e;
            margin-right: 8px;
        }

        .show-description {
            font-size: 13px;
            color: #cccccc;
            line-height: 1.6;
        }

        /* Master of Magic 特殊布局 */
        .master-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
            width: 300px;
            margin-bottom: 20px;
        }

        .master-grid-item {
            width: 100%;
            height: 200px; /* 1:2 比例 */
            background: linear-gradient(180deg, #1a1a1a, #2a2a2a);
            border: 1px solid #333333;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666666;
            font-size: 8px;
            letter-spacing: 1px;
            text-align: center;
        }

        .master-info {
            width: 100%;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 店铺介绍区域 -->
        <div class="shop-intro">
            <div class="shop-intro-content">
                <div class="shop-brand">謎·魔术酒吧</div>
                <div class="shop-tagline">始于2018，六年匠心。我们是鸡尾酒吧，也是魔术剧场。</div>
                <div class="shop-description">
                    一杯精心调制的鸡尾酒，一场近在咫尺的奇迹。<br>
                    在400平米的宽阔空间内，探索味觉与视觉的极限。
                </div>
                <div class="shop-location">
                    <div class="shop-location-label">Find Us</div>
                    <div class="shop-address">蔡锷路32号</div>
                </div>
            </div>
        </div>

        <!-- 创始人介绍区域 -->
        <div class="founder-section">
            <div class="section-title">创始人</div>
            <div class="founder-container">
                <div class="founder-photo">
                    PORTRAIT<br>1:2
                </div>
                <div class="founder-info">
                    <div class="founder-name">李柘翰</div>
                    <div class="founder-title">謎·魔术酒吧 创始人</div>
                    <ul class="founder-achievements">
                        <li>湖北高校魔术联盟 创始人</li>
                        <li>2009年 全民大魔竞 武汉地区 冠军</li>
                        <li>2012年 湖北首届杂技·魔术类比赛金奖</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>